from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.models import Product, User
from app.schemas.product import Product as ProductSchema, ProductCreate, ProductUpdate, ProductList
from app.core.deps import get_current_active_user

router = APIRouter()

@router.get("/", response_model=List[ProductList])
def read_products(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="Ürün adı ile arama"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Ürün listesini getir
    """
    query = db.query(Product)
    
    # Arama filtresi
    if search:
        query = query.filter(Product.urun_adi.contains(search))
    
    products = query.offset(skip).limit(limit).all()
    return products

@router.post("/", response_model=ProductSchema)
def create_product(
    product: ProductCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Yeni ürün oluştur
    """
    # Aynı isimde ürün kontrolü
    existing_product = db.query(Product).filter(Product.urun_adi == product.urun_adi).first()
    if existing_product:
        raise HTTPException(
            status_code=400,
            detail="Bu isimde bir ürün zaten mevcut"
        )
    
    db_product = Product(**product.dict())
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product

@router.get("/{product_id}", response_model=ProductSchema)
def read_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Ürün detayını getir
    """
    product = db.query(Product).filter(Product.id == product_id).first()
    if product is None:
        raise HTTPException(status_code=404, detail="Ürün bulunamadı")
    return product

@router.put("/{product_id}", response_model=ProductSchema)
def update_product(
    product_id: int,
    product: ProductUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Ürün bilgilerini güncelle
    """
    db_product = db.query(Product).filter(Product.id == product_id).first()
    if db_product is None:
        raise HTTPException(status_code=404, detail="Ürün bulunamadı")
    
    # Güncelleme verilerini uygula
    update_data = product.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_product, field, value)
    
    db.commit()
    db.refresh(db_product)
    return db_product

@router.delete("/{product_id}")
def delete_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Ürünü sil
    """
    db_product = db.query(Product).filter(Product.id == product_id).first()
    if db_product is None:
        raise HTTPException(status_code=404, detail="Ürün bulunamadı")
    
    # TODO: Ürüne ait satışlar varsa silme işlemini engelle
    # Bu kontrol sonraki aşamada satış modeli eklendikten sonra yapılacak
    
    db.delete(db_product)
    db.commit()
    return {"message": "Ürün başarıyla silindi"}

@router.get("/stok/dusuk", response_model=List[ProductList])
def get_low_stock_products(
    min_stock: float = Query(10, description="Minimum stok seviyesi"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Stoku düşük ürünleri getir
    """
    products = db.query(Product).filter(Product.mevcut_stok <= min_stock).all()
    return products
