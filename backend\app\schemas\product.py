from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class ProductBase(BaseModel):
    urun_adi: str
    urun_alis_fiyati: float = 0
    urun_fiyati: float
    mevcut_stok: float = 0
    nakliye_ucreti: float = 0

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    urun_adi: Optional[str] = None
    urun_alis_fiyati: Optional[float] = None
    urun_fiyati: Optional[float] = None
    mevcut_stok: Optional[float] = None
    nakliye_ucreti: Optional[float] = None

class Product(ProductBase):
    id: int
    eklenme_tarihi: datetime
    duzenleme_tarihi: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProductList(BaseModel):
    id: int
    urun_adi: str
    urun_fiyati: float
    mevcut_stok: float
    eklenme_tarihi: datetime

    class Config:
        from_attributes = True
