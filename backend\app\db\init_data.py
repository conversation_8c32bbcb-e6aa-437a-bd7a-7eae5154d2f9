from sqlalchemy.orm import Session
from app.db.database import SessionLocal
from app.models.models import <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
from app.core.security import get_password_hash

def init_database():
    """
    Veritabanını başlangıç verileri ile doldur
    """
    db = SessionLocal()
    
    try:
        # Admin kullanıcı oluştur
        admin_user = db.query(User).filter(User.kullanici_adi == "admin").first()
        if not admin_user:
            admin_user = User(
                kullanici_adi="admin",
                sifre=get_password_hash("admin123"),
                ad_soyad="Sistem Yöneticisi",
                email="<EMAIL>",
                rol="Yönetici",
                aktif=True
            )
            db.add(admin_user)
            print("Admin kullanıcı oluşturuldu: admin/admin123")
        
        # Temel il verileri
        iller_data = [
            "Adana", "Adıyaman", "Afyonkarahisar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>asya", "Ankara", "<PERSON>talya", "<PERSON>vin",
            "<PERSON>ydı<PERSON>", "Balıkesir", "Bilecik", "Bingöl", "Bitlis", "Bolu", "Burdur", "Bursa",
            "Çanakkale", "Çankırı", "Çorum", "Denizli", "Diyarbakır", "Edirne", "Elazığ", "Erzincan",
            "Erzurum", "Eskişehir", "Gaziantep", "Giresun", "Gümüşhane", "Hakkari", "Hatay", "Isparta",
            "Mersin", "İstanbul", "İzmir", "Kars", "Kastamonu", "Kayseri", "Kırklareli", "Kırşehir",
            "Kocaeli", "Konya", "Kütahya", "Malatya", "Manisa", "Kahramanmaraş", "Mardin", "Muğla",
            "Muş", "Nevşehir", "Niğde", "Ordu", "Rize", "Sakarya", "Samsun", "Siirt", "Sinop",
            "Sivas", "Tekirdağ", "Tokat", "Trabzon", "Tunceli", "Şanlıurfa", "Uşak", "Van",
            "Yozgat", "Zonguldak", "Aksaray", "Bayburt", "Karaman", "Kırıkkale", "Batman", "Şırnak",
            "Bartın", "Ardahan", "Iğdır", "Yalova", "Karabük", "Kilis", "Osmaniye", "Düzce"
        ]
        
        for il_adi in iller_data:
            existing_il = db.query(Il).filter(Il.ad == il_adi).first()
            if not existing_il:
                il = Il(ad=il_adi)
                db.add(il)
        
        # Aydın ili için örnek ilçeler (proje Aydın Silaj için)
        aydin_il = db.query(Il).filter(Il.ad == "Aydın").first()
        if aydin_il:
            aydin_ilceleri = [
                "Merkez", "Bozdoğan", "Buharkent", "Çine", "Didim", "Efeler", "Germencik",
                "İncirliova", "Karacasu", "Karpuzlu", "Koçarlı", "Köşk", "Kuşadası",
                "Kuyucak", "Nazilli", "Söke", "Sultanhisar", "Yenipazar"
            ]
            
            for ilce_adi in aydin_ilceleri:
                existing_ilce = db.query(Ilce).filter(
                    Ilce.ad == ilce_adi, 
                    Ilce.il_id == aydin_il.id
                ).first()
                if not existing_ilce:
                    ilce = Ilce(ad=ilce_adi, il_id=aydin_il.id)
                    db.add(ilce)
        
        db.commit()
        print("Başlangıç verileri başarıyla eklendi!")
        
    except Exception as e:
        print(f"Hata: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
