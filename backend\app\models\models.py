from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Float, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class User(Base):
    __tablename__ = "kullanicilar"
    
    id = Column(Integer, primary_key=True, index=True)
    kullanici_adi = Column(String(50), unique=True, index=True, nullable=False)
    sifre = Column(String(255), nullable=False)
    ad_soyad = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, index=True)
    telefon = Column(String(15))
    rol = Column(String(20), default="Kullanıcı")  # Yönetici/Kullanıcı/Misafir
    aktif = Column(Boolean, default=True)
    son_giris_tarihi = Column(DateTime)
    kayit_tarihi = Column(DateTime, default=func.now())
    guncelleme_tarihi = Column(DateTime, default=func.now(), onupdate=func.now())
    profil_resmi = Column(String(255))
    aciklama = Column(Text)

class Il(Base):
    __tablename__ = "iller"
    
    id = Column(Integer, primary_key=True, index=True)
    ad = Column(String(50), nullable=False)
    
    # İlişkiler
    ilceler = relationship("Ilce", back_populates="il")
    musteriler = relationship("Customer", back_populates="il")

class Ilce(Base):
    __tablename__ = "ilceler"
    
    id = Column(Integer, primary_key=True, index=True)
    ad = Column(String(50), nullable=False)
    il_id = Column(Integer, ForeignKey("iller.id"), nullable=False)
    
    # İlişkiler
    il = relationship("Il", back_populates="ilceler")
    mahalleler = relationship("Mahalle", back_populates="ilce")
    musteriler = relationship("Customer", back_populates="ilce")

class Mahalle(Base):
    __tablename__ = "mahalleler"
    
    id = Column(Integer, primary_key=True, index=True)
    ad = Column(String(50), nullable=False)
    ilce_id = Column(Integer, ForeignKey("ilceler.id"), nullable=False)
    
    # İlişkiler
    ilce = relationship("Ilce", back_populates="mahalleler")
    musteriler = relationship("Customer", back_populates="mahalle")

class Customer(Base):
    __tablename__ = "musteriler"
    
    id = Column(Integer, primary_key=True, index=True)
    ad_soyad = Column(String(100), nullable=False)
    telefon = Column(String(15), nullable=False)
    yas = Column(Integer)
    il_id = Column(Integer, ForeignKey("iller.id"))
    ilce_id = Column(Integer, ForeignKey("ilceler.id"))
    mahalle_id = Column(Integer, ForeignKey("mahalleler.id"))
    kucuk_bas_hayvan_sayisi = Column(Integer, default=0)
    buyuk_bas_hayvan_sayisi = Column(Integer, default=0)
    kayit_tarihi = Column(DateTime, default=func.now())
    duzenleme_tarihi = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # İlişkiler
    il = relationship("Il", back_populates="musteriler")
    ilce = relationship("Ilce", back_populates="musteriler")
    mahalle = relationship("Mahalle", back_populates="musteriler")

class Product(Base):
    __tablename__ = "urunler"
    
    id = Column(Integer, primary_key=True, index=True)
    urun_adi = Column(String(100), nullable=False)
    urun_alis_fiyati = Column(Float, default=0)
    urun_fiyati = Column(Float, nullable=False)
    mevcut_stok = Column(Float, default=0)
    nakliye_ucreti = Column(Float, default=0)
    eklenme_tarihi = Column(DateTime, default=func.now())
    duzenleme_tarihi = Column(DateTime, default=func.now(), onupdate=func.now())
