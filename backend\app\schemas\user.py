from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    kullanici_adi: str
    ad_soyad: str
    email: Optional[EmailStr] = None
    telefon: Optional[str] = None
    rol: str = "Kullanıcı"
    aktif: bool = True
    profil_resmi: Optional[str] = None
    aciklama: Optional[str] = None

class UserCreate(UserBase):
    sifre: str

class UserUpdate(BaseModel):
    kullanici_adi: Optional[str] = None
    ad_soyad: Optional[str] = None
    email: Optional[EmailStr] = None
    telefon: Optional[str] = None
    rol: Optional[str] = None
    aktif: Optional[bool] = None
    profil_resmi: Optional[str] = None
    aciklama: Optional[str] = None

class UserChangePassword(BaseModel):
    eski_sifre: str
    yeni_sifre: str

class User(UserBase):
    id: int
    son_giris_tarihi: Optional[datetime] = None
    kayit_tarihi: datetime
    guncelleme_tarihi: Optional[datetime] = None

    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserLogin(BaseModel):
    kullanici_adi: str
    sifre: str
