from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from app.schemas.address import Il, Ilce, Mahalle

class CustomerBase(BaseModel):
    ad_soyad: str
    telefon: str
    yas: Optional[int] = None
    il_id: Optional[int] = None
    ilce_id: Optional[int] = None
    mahalle_id: Optional[int] = None
    kucuk_bas_hayvan_sayisi: int = 0
    buyuk_bas_hayvan_sayisi: int = 0

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(BaseModel):
    ad_soyad: Optional[str] = None
    telefon: Optional[str] = None
    yas: Optional[int] = None
    il_id: Optional[int] = None
    ilce_id: Optional[int] = None
    mahalle_id: Optional[int] = None
    kucuk_bas_hayvan_sayisi: Optional[int] = None
    buyuk_bas_hayvan_sayisi: Optional[int] = None

class Customer(CustomerBase):
    id: int
    kayit_tarihi: datetime
    duzenleme_tarihi: Optional[datetime] = None
    il: Optional[Il] = None
    ilce: Optional[Ilce] = None
    mahalle: Optional[Mahalle] = None

    class Config:
        from_attributes = True

class CustomerList(BaseModel):
    id: int
    ad_soyad: str
    telefon: str
    il_adi: Optional[str] = None
    ilce_adi: Optional[str] = None
    mahalle_adi: Optional[str] = None
    kayit_tarihi: datetime

    class Config:
        from_attributes = True
