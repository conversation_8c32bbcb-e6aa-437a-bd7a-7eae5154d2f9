from datetime import timedel<PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>RequestForm
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.config import settings
from app.core.security import create_access_token, verify_password, get_password_hash
from app.models.models import User
from app.schemas.user import Token, UserLogin, User as UserSchema
from app.core.deps import get_current_active_user

router = APIRouter()

@router.post("/login", response_model=Token)
def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """
    Kullanıcı girişi
    """
    user = db.query(User).filter(User.kullanici_adi == user_credentials.kullanici_adi).first()
    
    if not user or not verify_password(user_credentials.sifre, user.sifre):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="<PERSON><PERSON>ı<PERSON><PERSON> adı veya şifre hatalı",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.aktif:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Kullanıcı aktif değil"
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.kullanici_adi}, expires_delta=access_token_expires
    )
    
    # Son giriş tarihini güncelle
    from sqlalchemy.sql import func
    user.son_giris_tarihi = func.now()
    db.commit()
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/logout")
def logout():
    """
    Kullanıcı çıkışı (client-side token silme)
    """
    return {"message": "Başarıyla çıkış yapıldı"}

@router.get("/me", response_model=UserSchema)
def read_users_me(current_user: User = Depends(get_current_active_user)):
    """
    Mevcut kullanıcı bilgilerini getir
    """
    return current_user
