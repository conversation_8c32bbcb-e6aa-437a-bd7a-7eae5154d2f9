"""
KobiPanel SQLite Veritabanı Seed Data Script
Bu script, yeni SQLite veritabanına sahte veriler ekler.
"""

import sqlite3
import hashlib
from datetime import datetime, timedelta
import random
from faker import Faker

# Faker instance oluştur
fake = Faker('tr_TR')  # Türkçe locale

def hash_password(password: str) -> str:
    """Şifreyi SHA256 ile hashle"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_seed_data(db_path: str = "kobipanel.db"):
    """SQLite veritabanına sahte veriler ekle"""
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🌱 Seed data ekleniyor...")
        
        # 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ekle
        print("👥 Kullanıcılar ekleniyor...")
        users_data = [
            ("ismail", hash_password("1jkdhdkj"), "<PERSON><PERSON><PERSON>", "<EMAIL>", "5551234567", "Yönet<PERSON>", True),
            ("aleyna", hash_password("1jkdhdkj"), "<PERSON><PERSON><PERSON>mge Pehlevan", "<EMAIL>", "5551234568", "Kullanıcı", True),
            ("teslime", hash_password("4070"), "Teslime Pehlevan", "<EMAIL>", "5551234569", "Kullanıcı", True),
            ("ahmet", hash_password("3246"), "Ahmet Pehlevan", "<EMAIL>", "5551234570", "Kullanıcı", True),
            ("mustafa", hash_password("4070"), "Mustafa Pehlevan", "<EMAIL>", "5551234571", "Kullanıcı", True),
            ("arda", hash_password("123456"), "Arda Koca", "<EMAIL>", "5551234572", "Misafir", True),
        ]
        
        cursor.executemany("""
            INSERT INTO users (kullanici_adi, sifre, ad_soyad, email, telefon, rol, aktif, kayit_tarihi)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, [(u[0], u[1], u[2], u[3], u[4], u[5], u[6], datetime.now()) for u in users_data])
        
        # 2. İl verilerini ekle (Türkiye'nin bazı illeri)
        print("🏙️ İl verileri ekleniyor...")
        il_data = [
            (1, "ADANA"), (2, "ADIYAMAN"), (3, "AFYONKARAHİSAR"), (4, "AĞRI"), (5, "AKSARAY"),
            (6, "AMASYA"), (7, "ANKARA"), (8, "ANTALYA"), (9, "ARDAHAN"), (10, "ARTVİN"),
            (11, "AYDIN"), (12, "BALIKESİR"), (13, "BARTIN"), (14, "BATMAN"), (15, "BAYBURT"),
            (16, "BİLECİK"), (17, "BİNGÖL"), (18, "BİTLİS"), (19, "BOLU"), (20, "BURDUR"),
            (21, "BURSA"), (22, "ÇANAKKALE"), (23, "ÇANKIRI"), (24, "ÇORUM"), (25, "DENİZLİ"),
            (26, "DİYARBAKIR"), (27, "DÜZCE"), (28, "EDİRNE"), (29, "ELAZIĞ"), (30, "ERZİNCAN"),
            (31, "ERZURUM"), (32, "ESKİŞEHİR"), (33, "GAZİANTEP"), (34, "GİRESUN"), (35, "GÜMÜŞHANE"),
            (36, "HAKKARİ"), (37, "HATAY"), (38, "IĞDIR"), (39, "ISPARTA"), (40, "İSTANBUL"),
            (41, "İZMİR"), (42, "KAHRAMANMARAŞ"), (43, "KARABÜK"), (44, "KARAMAN"), (45, "KARS"),
            (46, "KASTAMONU"), (47, "KAYSERİ"), (48, "KIRIKKALE"), (49, "KIRKLARELİ"), (50, "KIRŞEHİR"),
            (51, "KİLİS"), (52, "KOCAELİ"), (53, "KONYA"), (54, "KÜTAHYA"), (55, "MALATYA"),
            (56, "MANİSA"), (57, "MARDİN"), (58, "MERSİN"), (59, "MUĞLA"), (60, "MUŞ"),
            (61, "NEVŞEHİR"), (62, "NİĞDE"), (63, "ORDU"), (64, "OSMANİYE"), (65, "RİZE"),
            (66, "SAKARYA"), (67, "SAMSUN"), (68, "SİİRT"), (69, "SİNOP"), (70, "SİVAS"),
            (71, "ŞANLIURFA"), (72, "ŞIRNAK"), (73, "TEKİRDAĞ"), (74, "TOKAT"), (75, "TRABZON"),
            (76, "TUNCELİ"), (77, "UŞAK"), (78, "VAN"), (79, "YALOVA"), (80, "YOZGAT"), (81, "ZONGULDAK")
        ]
        
        cursor.executemany("INSERT INTO il (id, ad) VALUES (?, ?)", il_data)
        
        # 3. İlçe verilerini ekle (Aydın ili örneği)
        print("🏘️ İlçe verileri ekleniyor...")
        ilce_data = [
            (125, 11, "BOZDOĞAN"), (126, 11, "BUHARKENT"), (127, 11, "ÇİNE"), (128, 11, "DİDİM"),
            (129, 11, "EFELER"), (130, 11, "GERMENCİK"), (131, 11, "İNCİRLİOVA"), (132, 11, "KARACASU"),
            (133, 11, "KARPUZLU"), (134, 11, "KOÇARLI"), (135, 11, "KÖŞK"), (136, 11, "KUŞADASI"),
            (137, 11, "KUYUCAK"), (138, 11, "NAZİLLİ"), (139, 11, "SÖKE"), (140, 11, "SULTANHİSAR"),
            (141, 11, "YENİPAZAR")
        ]
        
        cursor.executemany("INSERT INTO ilce (id, ad, il_id) VALUES (?, ?, ?)", ilce_data)
        
        # 4. Mahalle verilerini ekle (Efeler ilçesi örneği)
        print("🏠 Mahalle verileri ekleniyor...")
        mahalle_data = [
            (9937, "MERKEZ MAH", 129), (9938, "YENİ MAH", 129), (9939, "ATATÜRK MAH", 129),
            (9940, "CUMHURİYET MAH", 129), (9941, "ZAFER MAH", 129), (9942, "İSTİKLAL MAH", 129),
            (9943, "GÜZELYURT MAH", 129), (9944, "IŞIKLAR MAH", 129), (9945, "KÖPRÜLÜ MAH", 129),
            (9946, "OVAEYMIR MAH", 129)
        ]
        
        cursor.executemany("INSERT INTO mahalle (id, ad, ilce_id) VALUES (?, ?, ?)", mahalle_data)
        
        # 5. Ürünler ekle
        print("📦 Ürünler ekleniyor...")
        products_data = [
            ("Büyük Mısır Silajı", 350, 400, 1000, 30, datetime.now()),
            ("Küçük Mısır Silajı", 180, 200, 1000, 20, datetime.now()),
            ("Mısır Sapı", 220, 250, 1000, 30, datetime.now()),
            ("Arpa Silajı", 300, 350, 800, 25, datetime.now()),
            ("Buğday Silajı", 280, 320, 600, 25, datetime.now()),
            ("Yonca Silajı", 400, 450, 500, 35, datetime.now()),
        ]
        
        cursor.executemany("""
            INSERT INTO products (urun_adi, alis_fiyati, satis_fiyati, mevcut_stok, nakliye_ucreti, eklenme_tarihi, duzenlenme_tarihi)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, [(p[0], p[1], p[2], p[3], p[4], p[5], p[5]) for p in products_data])
        
        # 6. Müşteriler ekle
        print("👤 Müşteriler ekleniyor...")
        for i in range(50):
            customer_data = (
                fake.name(),
                fake.phone_number(),
                random.randint(25, 70),
                11,  # Aydın ili
                129,  # Efeler ilçesi
                random.choice([9937, 9938, 9939, 9940, 9941]),  # Random mahalle
                random.randint(1, 100),  # Küçükbaş hayvan sayısı
                random.randint(1, 50),   # Büyükbaş hayvan sayısı
                datetime.now(),
                datetime.now()
            )
            
            cursor.execute("""
                INSERT INTO customers (ad_soyad, telefon, yas, il_id, ilce_id, mahalle_id, 
                                     kucukbas_hayvan_sayisi, buyukbas_hayvan_sayisi, kayit_tarihi, duzenlenme_tarihi)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, customer_data)
        
        # 7. Satışlar ekle
        print("💰 Satışlar ekleniyor...")
        for i in range(100):
            sale_data = (
                random.randint(1, 6),    # Ürün ID
                random.randint(1, 50),   # Müşteri ID
                random.randint(1, 10),   # Ürün adeti
                random.randint(200, 4200),  # Tutar
                random.randint(0, 100),  # İndirim
                random.choice([True, False]),  # Servis mi
                random.choice([True, False]),  # Kredi kartı mı
                fake.date_between(start_date='-1y', end_date='today'),  # Satış tarihi
                random.choice([True, False]),  # Teslim edildi mi
                random.randint(20, 50),  # Nakliye bedeli
                fake.sentence(nb_words=6),  # Açıklama
                datetime.now()
            )
            
            cursor.execute("""
                INSERT INTO sales (urun_id, musteri_id, urun_adeti, tutar, indirim, servis_mi, 
                                 kredi_karti_mi, satis_tarihi, teslim_edildi_mi, nakliye_bedeli, 
                                 aciklama, kayit_tarihi)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, sale_data)
        
        # 8. Giderler ekle
        print("💸 Giderler ekleniyor...")
        for i in range(30):
            expense_data = (
                fake.sentence(nb_words=3),  # Konu
                random.randint(100, 5000),  # Tutar
                fake.date_between(start_date='-1y', end_date='today'),  # Tarih
                datetime.now()
            )
            
            cursor.execute("""
                INSERT INTO expenses (konu, tutar, tarih, kayit_tarihi)
                VALUES (?, ?, ?, ?)
            """, expense_data)
        
        # 9. Notlar ekle
        print("📝 Notlar ekleniyor...")
        for i in range(20):
            note_data = (
                fake.sentence(nb_words=4),  # Konu
                fake.text(max_nb_chars=200),  # Açıklama
                fake.date_between(start_date='-6m', end_date='today'),  # Tarih
                datetime.now()
            )
            
            cursor.execute("""
                INSERT INTO notes (konu, aciklama, tarih, kayit_tarihi)
                VALUES (?, ?, ?, ?)
            """, note_data)
        
        # 10. Araçlar ekle
        print("🚗 Araçlar ekleniyor...")
        vehicles_data = [
            ("09 ABC 123", "Ford", "Transit", 2018, "Beyaz", True, "TR123456789", "WFOZXXGCDZ123456",
             datetime(2024, 6, 15), datetime(2024, 8, 20), datetime(2024, 12, 10)),
            ("09 DEF 456", "Mercedes", "Sprinter", 2020, "Gri", True, "TR987654321", "WDB9066351234567",
             datetime(2024, 9, 20), datetime(2024, 11, 15), datetime(2025, 1, 5)),
            ("09 GHI 789", "Iveco", "Daily", 2019, "Kırmızı", True, "TR456789123", "ZCFC35A0012345678",
             datetime(2024, 7, 30), datetime(2024, 10, 25), datetime(2024, 11, 30)),
        ]

        for vehicle in vehicles_data:
            cursor.execute("""
                INSERT INTO vehicles (plaka, marka, model, yil, renk, aktif, tescil_no, sase_no,
                                    vize_bitis_tarihi, sigorta_bitis_tarihi, egzoz_muayene_tarihi, kayit_tarihi)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (*vehicle, datetime.now()))

        # 11. Yakıt kayıtları ekle
        print("⛽ Yakıt kayıtları ekleniyor...")
        for i in range(50):
            fuel_data = (
                random.randint(1, 3),  # Araç ID
                random.randint(30, 80),  # Litre
                random.uniform(25.0, 35.0),  # Litre fiyatı
                fake.date_between(start_date='-6m', end_date='today'),  # Tarih
                fake.sentence(nb_words=3),  # Açıklama
                datetime.now()
            )

            cursor.execute("""
                INSERT INTO fuel_records (arac_id, litre, litre_fiyati, tarih, aciklama, kayit_tarihi)
                VALUES (?, ?, ?, ?, ?, ?)
            """, fuel_data)

        # 12. Biçilen tarlalar ekle
        print("🌾 Biçilen tarlalar ekleniyor...")
        for i in range(20):
            field_data = (
                fake.city(),  # Tarla adı/konumu
                random.randint(1, 50),  # Müşteri ID
                random.randint(5, 50),  # Dönüm
                fake.date_between(start_date='-3m', end_date='today'),  # Biçim tarihi
                random.randint(1, 3),  # Araç ID
                fake.sentence(nb_words=4),  # Açıklama
                datetime.now()
            )

            cursor.execute("""
                INSERT INTO cut_fields (tarla_adi, musteri_id, donum, bicim_tarihi, arac_id, aciklama, kayit_tarihi)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, field_data)

        # 13. Kantar fişleri ekle
        print("⚖️ Kantar fişleri ekleniyor...")
        for i in range(30):
            weighing_data = (
                random.randint(1, 20),  # Tarla ID
                random.randint(1, 6),   # Ürün ID
                random.randint(1000, 5000),  # Brüt ağırlık (kg)
                random.randint(800, 1200),   # Dara (kg)
                fake.date_between(start_date='-3m', end_date='today'),  # Tarih
                fake.sentence(nb_words=3),  # Açıklama
                datetime.now()
            )

            cursor.execute("""
                INSERT INTO weighing_slips (tarla_id, urun_id, brut_agirlik, dara, tarih, aciklama, kayit_tarihi)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, weighing_data)

        conn.commit()
        print("✅ Seed data başarıyla eklendi!")
        print(f"📊 Toplam eklenen veriler:")
        print(f"   👥 Kullanıcılar: 6")
        print(f"   🏙️ İller: 81")
        print(f"   🏘️ İlçeler: 17 (Aydın)")
        print(f"   🏠 Mahalleler: 10 (Efeler)")
        print(f"   📦 Ürünler: 6")
        print(f"   👤 Müşteriler: 50")
        print(f"   💰 Satışlar: 100")
        print(f"   💸 Giderler: 30")
        print(f"   📝 Notlar: 20")
        print(f"   🚗 Araçlar: 3")
        print(f"   ⛽ Yakıt kayıtları: 50")
        print(f"   🌾 Biçilen tarlalar: 20")
        print(f"   ⚖️ Kantar fişleri: 30")

    except Exception as e:
        print(f"❌ Hata oluştu: {e}")
        conn.rollback()
    finally:
        conn.close()

def create_admin_user(db_path: str = "kobipanel.db"):
    """Sadece admin kullanıcısı oluştur"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        admin_data = (
            "admin",
            hash_password("admin123"),
            "Sistem Yöneticisi",
            "<EMAIL>",
            "5551234567",
            "Admin",
            True,
            datetime.now()
        )

        cursor.execute("""
            INSERT INTO users (kullanici_adi, sifre, ad_soyad, email, telefon, rol, aktif, kayit_tarihi)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, admin_data)

        conn.commit()
        print("✅ Admin kullanıcısı oluşturuldu!")
        print("   👤 Kullanıcı adı: admin")
        print("   🔑 Şifre: admin123")

    except Exception as e:
        print(f"❌ Hata oluştu: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--admin-only":
        create_admin_user()
    else:
        create_seed_data()
