from pydantic import BaseModel
from typing import List, Optional

class IlBase(BaseModel):
    ad: str

class IlCreate(IlBase):
    pass

class Il(IlBase):
    id: int

    class Config:
        from_attributes = True

class IlceBase(BaseModel):
    ad: str
    il_id: int

class IlceCreate(IlceBase):
    pass

class Ilce(IlceBase):
    id: int
    il: Optional[Il] = None

    class Config:
        from_attributes = True

class MahalleBase(BaseModel):
    ad: str
    ilce_id: int

class MahalleCreate(MahalleBase):
    pass

class Mahalle(MahalleBase):
    id: int
    ilce: Optional[Ilce] = None

    class Config:
        from_attributes = True

# Cascade dropdown için özel şemalar
class IlWithIlceler(Il):
    ilceler: List[Ilce] = []

class IlceWithMahalleler(Ilce):
    mahalleler: List[Mahalle] = []
